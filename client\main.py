"""
نظام إدارة كاميرات المراقبة الأمنية - تطبيق العميل
Security Camera Management System - Client Application
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import requests
import cv2
import threading
import time
from PIL import Image, ImageTk
import json
from datetime import datetime
import os

class SecurityCameraClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة كاميرات المراقبة الأمنية")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')
        
        # إعدادات الاتصال
        self.api_base_url = "http://localhost:8000/api"
        self.cameras = []
        self.active_streams = {}
        self.stream_threads = {}
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
        self.load_cameras()
        
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        
        # شريط العنوان
        title_frame = tk.Frame(self.root, bg='#34495e', height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🎥 نظام إدارة كاميرات المراقبة الأمنية",
            font=('Arial', 16, 'bold'),
            bg='#34495e',
            fg='white'
        )
        title_label.pack(pady=15)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # الشريط الجانبي للتحكم
        control_frame = tk.Frame(main_frame, bg='#34495e', width=300)
        control_frame.pack(side='left', fill='y', padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # عنوان قائمة الكاميرات
        cameras_label = tk.Label(
            control_frame,
            text="📹 قائمة الكاميرات",
            font=('Arial', 12, 'bold'),
            bg='#34495e',
            fg='white'
        )
        cameras_label.pack(pady=10)
        
        # قائمة الكاميرات
        self.cameras_listbox = tk.Listbox(
            control_frame,
            font=('Arial', 10),
            bg='#ecf0f1',
            selectmode='single',
            height=15
        )
        self.cameras_listbox.pack(fill='both', expand=True, padx=10, pady=5)
        self.cameras_listbox.bind('<Double-Button-1>', self.on_camera_select)
        
        # أزرار التحكم
        buttons_frame = tk.Frame(control_frame, bg='#34495e')
        buttons_frame.pack(fill='x', padx=10, pady=10)
        
        # زر إضافة كاميرا
        add_btn = tk.Button(
            buttons_frame,
            text="➕ إضافة كاميرا",
            command=self.add_camera_dialog,
            bg='#27ae60',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=10,
            pady=5
        )
        add_btn.pack(fill='x', pady=2)
        
        # زر حذف كاميرا
        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف كاميرا",
            command=self.delete_camera,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=10,
            pady=5
        )
        delete_btn.pack(fill='x', pady=2)
        
        # زر تحديث القائمة
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄 تحديث القائمة",
            command=self.load_cameras,
            bg='#3498db',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=10,
            pady=5
        )
        refresh_btn.pack(fill='x', pady=2)
        
        # زر عرض السجلات
        logs_btn = tk.Button(
            buttons_frame,
            text="📊 عرض السجلات",
            command=self.show_logs,
            bg='#9b59b6',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=10,
            pady=5
        )
        logs_btn.pack(fill='x', pady=2)
        
        # منطقة عرض البث المباشر
        self.video_frame = tk.Frame(main_frame, bg='#1a252f')
        self.video_frame.pack(side='right', fill='both', expand=True)
        
        # تسمية منطقة الفيديو
        self.video_label = tk.Label(
            self.video_frame,
            text="اختر كاميرا لعرض البث المباشر",
            font=('Arial', 14),
            bg='#1a252f',
            fg='white'
        )
        self.video_label.pack(expand=True)
        
        # شريط الحالة
        status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            font=('Arial', 10),
            bg='#34495e',
            fg='white'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # معلومات الاتصال
        connection_label = tk.Label(
            status_frame,
            text=f"متصل بـ: {self.api_base_url}",
            font=('Arial', 10),
            bg='#34495e',
            fg='#bdc3c7'
        )
        connection_label.pack(side='right', padx=10, pady=5)
    
    def load_cameras(self):
        """تحميل قائمة الكاميرات من السيرفر"""
        try:
            response = requests.get(f"{self.api_base_url}/cameras", timeout=5)
            if response.status_code == 200:
                self.cameras = response.json()
                self.update_cameras_list()
                self.status_label.config(text=f"تم تحميل {len(self.cameras)} كاميرا")
            else:
                messagebox.showerror("خطأ", "فشل في تحميل قائمة الكاميرات")
                self.status_label.config(text="خطأ في الاتصال")
        except requests.exceptions.RequestException as e:
            messagebox.showerror("خطأ في الاتصال", f"لا يمكن الاتصال بالسيرفر:\n{str(e)}")
            self.status_label.config(text="غير متصل")
    
    def update_cameras_list(self):
        """تحديث قائمة الكاميرات في الواجهة"""
        self.cameras_listbox.delete(0, tk.END)
        for camera in self.cameras:
            status = "🟢" if camera['is_active'] else "🔴"
            recording = "🔴 يسجل" if camera.get('is_recording') else ""
            display_text = f"{status} {camera['name']} - {camera['ip_address']} {recording}"
            self.cameras_listbox.insert(tk.END, display_text)
    
    def on_camera_select(self, event):
        """عند اختيار كاميرا من القائمة"""
        selection = self.cameras_listbox.curselection()
        if selection:
            camera_index = selection[0]
            camera = self.cameras[camera_index]
            self.start_camera_stream(camera)
    
    def start_camera_stream(self, camera):
        """بدء عرض البث المباشر للكاميرا"""
        camera_id = camera['id']
        
        # إيقاف البث السابق إن وجد
        self.stop_all_streams()
        
        # بدء البث الجديد
        if camera['is_active']:
            self.status_label.config(text=f"جاري الاتصال بالكاميرا: {camera['name']}")
            
            # إنشاء thread للبث
            stream_thread = threading.Thread(
                target=self.stream_camera,
                args=(camera,),
                daemon=True
            )
            stream_thread.start()
            self.stream_threads[camera_id] = stream_thread
        else:
            messagebox.showwarning("تحذير", "الكاميرا غير نشطة")
    
    def stream_camera(self, camera):
        """عرض البث المباشر للكاميرا"""
        try:
            # محاولة الاتصال بالكاميرا
            cap = cv2.VideoCapture(camera['rtsp_url'])
            
            if not cap.isOpened():
                self.root.after(0, lambda: self.status_label.config(text="فشل في الاتصال بالكاميرا"))
                return
            
            self.active_streams[camera['id']] = cap
            self.root.after(0, lambda: self.status_label.config(text=f"متصل بالكاميرا: {camera['name']}"))
            
            while camera['id'] in self.active_streams:
                ret, frame = cap.read()
                if ret:
                    # تغيير حجم الإطار
                    frame = cv2.resize(frame, (640, 480))
                    
                    # تحويل من BGR إلى RGB
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    
                    # تحويل إلى PIL Image
                    pil_image = Image.fromarray(frame_rgb)
                    photo = ImageTk.PhotoImage(pil_image)
                    
                    # تحديث الصورة في الواجهة
                    self.root.after(0, lambda p=photo: self.update_video_display(p))
                    
                    time.sleep(0.033)  # ~30 FPS
                else:
                    break
            
            cap.release()
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("خطأ", f"خطأ في البث: {str(e)}"))
    
    def update_video_display(self, photo):
        """تحديث عرض الفيديو"""
        self.video_label.config(image=photo, text="")
        self.video_label.image = photo  # الاحتفاظ بمرجع للصورة
    
    def stop_all_streams(self):
        """إيقاف جميع البث المباشر"""
        for camera_id in list(self.active_streams.keys()):
            if camera_id in self.active_streams:
                self.active_streams[camera_id].release()
                del self.active_streams[camera_id]
        
        self.video_label.config(image="", text="اختر كاميرا لعرض البث المباشر")
        self.video_label.image = None
    
    def add_camera_dialog(self):
        """نافذة إضافة كاميرا جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة كاميرا جديدة")
        dialog.geometry("400x500")
        dialog.configure(bg='#34495e')
        dialog.transient(self.root)
        dialog.grab_set()
        
        # حقول الإدخال
        fields = [
            ("اسم الكاميرا:", "name"),
            ("الوصف:", "description"),
            ("عنوان IP:", "ip_address"),
            ("المنفذ:", "port"),
            ("اسم المستخدم:", "username"),
            ("كلمة المرور:", "password"),
            ("رابط RTSP:", "rtsp_url"),
            ("الموقع:", "location")
        ]
        
        entries = {}
        
        for i, (label_text, field_name) in enumerate(fields):
            tk.Label(dialog, text=label_text, bg='#34495e', fg='white', font=('Arial', 10)).pack(pady=5)
            
            if field_name == "password":
                entry = tk.Entry(dialog, show="*", width=40)
            else:
                entry = tk.Entry(dialog, width=40)
            
            entry.pack(pady=2)
            entries[field_name] = entry
        
        # تعيين قيم افتراضية
        entries["port"].insert(0, "554")
        entries["rtsp_url"].insert(0, "rtsp://")
        
        def save_camera():
            camera_data = {}
            for field_name, entry in entries.items():
                value = entry.get().strip()
                if field_name == "port" and value:
                    camera_data[field_name] = int(value)
                elif value:
                    camera_data[field_name] = value
            
            if not camera_data.get("name") or not camera_data.get("ip_address"):
                messagebox.showerror("خطأ", "يجب إدخال اسم الكاميرا وعنوان IP")
                return
            
            try:
                response = requests.post(f"{self.api_base_url}/cameras", json=camera_data)
                if response.status_code == 200:
                    messagebox.showinfo("نجح", "تم إضافة الكاميرا بنجاح")
                    dialog.destroy()
                    self.load_cameras()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة الكاميرا")
            except requests.exceptions.RequestException as e:
                messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
        
        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(dialog, bg='#34495e')
        buttons_frame.pack(pady=20)
        
        tk.Button(buttons_frame, text="حفظ", command=save_camera, bg='#27ae60', fg='white').pack(side='left', padx=10)
        tk.Button(buttons_frame, text="إلغاء", command=dialog.destroy, bg='#e74c3c', fg='white').pack(side='left', padx=10)
    
    def delete_camera(self):
        """حذف الكاميرا المحددة"""
        selection = self.cameras_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار كاميرا للحذف")
            return
        
        camera_index = selection[0]
        camera = self.cameras[camera_index]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الكاميرا '{camera['name']}'؟"):
            try:
                response = requests.delete(f"{self.api_base_url}/cameras/{camera['id']}")
                if response.status_code == 200:
                    messagebox.showinfo("نجح", "تم حذف الكاميرا بنجاح")
                    self.load_cameras()
                    self.stop_all_streams()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف الكاميرا")
            except requests.exceptions.RequestException as e:
                messagebox.showerror("خطأ", f"خطأ في الاتصال: {str(e)}")
    
    def show_logs(self):
        """عرض سجلات المشاهدة والأنشطة"""
        messagebox.showinfo("قريباً", "ميزة عرض السجلات ستكون متاحة قريباً")
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        self.stop_all_streams()
        self.root.destroy()

if __name__ == "__main__":
    app = SecurityCameraClient()
    app.run()
