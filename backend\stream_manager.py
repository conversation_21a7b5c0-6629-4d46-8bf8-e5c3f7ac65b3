"""
مدير البث المباشر - Stream Manager
إدارة البث المباشر من الكاميرات ومعالجة الفيديو
"""

import cv2
import threading
import time
import os
import sqlite3
from datetime import datetime, timedelta
import numpy as np
from typing import Dict, List, Optional
import logging
from pathlib import Path

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CameraStream:
    """فئة إدارة بث كاميرا واحدة"""
    
    def __init__(self, camera_id: int, rtsp_url: str, name: str):
        self.camera_id = camera_id
        self.rtsp_url = rtsp_url
        self.name = name
        self.is_active = False
        self.is_recording = False
        self.cap = None
        self.stream_thread = None
        self.record_thread = None
        self.last_frame = None
        self.fps = 25
        self.resolution = (1920, 1080)
        self.motion_detector = None
        self.recording_writer = None
        self.recording_start_time = None
        
        # إعدادات كشف الحركة
        self.motion_threshold = 1000
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(detectShadows=True)
        
    def start_stream(self):
        """بدء البث المباشر"""
        if self.is_active:
            return False
            
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            if not self.cap.isOpened():
                logger.error(f"فشل في الاتصال بالكاميرا {self.name}")
                return False
            
            # تعيين خصائص الكاميرا
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.resolution[0])
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.resolution[1])
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            self.is_active = True
            self.stream_thread = threading.Thread(target=self._stream_loop, daemon=True)
            self.stream_thread.start()
            
            logger.info(f"تم بدء البث للكاميرا {self.name}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء البث للكاميرا {self.name}: {str(e)}")
            return False
    
    def stop_stream(self):
        """إيقاف البث المباشر"""
        self.is_active = False
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        if self.recording_writer:
            self.recording_writer.release()
            self.recording_writer = None
        
        logger.info(f"تم إيقاف البث للكاميرا {self.name}")
    
    def _stream_loop(self):
        """حلقة البث الرئيسية"""
        while self.is_active and self.cap:
            try:
                ret, frame = self.cap.read()
                if not ret:
                    logger.warning(f"فقدان الإطار من الكاميرا {self.name}")
                    time.sleep(0.1)
                    continue
                
                self.last_frame = frame.copy()
                
                # كشف الحركة
                motion_detected = self._detect_motion(frame)
                
                # التسجيل التلقائي عند كشف الحركة
                if motion_detected and not self.is_recording:
                    self.start_recording()
                
                # كتابة الإطار في ملف التسجيل
                if self.is_recording and self.recording_writer:
                    self.recording_writer.write(frame)
                
                time.sleep(1.0 / self.fps)
                
            except Exception as e:
                logger.error(f"خطأ في حلقة البث للكاميرا {self.name}: {str(e)}")
                time.sleep(1)
    
    def _detect_motion(self, frame) -> bool:
        """كشف الحركة في الإطار"""
        try:
            # تطبيق خوارزمية كشف الحركة
            fg_mask = self.background_subtractor.apply(frame)
            
            # حساب كمية الحركة
            motion_area = cv2.countNonZero(fg_mask)
            
            return motion_area > self.motion_threshold
            
        except Exception as e:
            logger.error(f"خطأ في كشف الحركة للكاميرا {self.name}: {str(e)}")
            return False
    
    def start_recording(self):
        """بدء التسجيل"""
        if self.is_recording:
            return
        
        try:
            # إنشاء مجلد التسجيلات
            recordings_dir = Path("storage/recordings")
            recordings_dir.mkdir(parents=True, exist_ok=True)
            
            # اسم ملف التسجيل
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"camera_{self.camera_id}_{timestamp}.mp4"
            filepath = recordings_dir / filename
            
            # إعداد كاتب الفيديو
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.recording_writer = cv2.VideoWriter(
                str(filepath), fourcc, self.fps, self.resolution
            )
            
            self.is_recording = True
            self.recording_start_time = datetime.now()
            
            # تسجيل في قاعدة البيانات
            self._save_recording_info(filename, str(filepath))
            
            logger.info(f"بدء التسجيل للكاميرا {self.name}: {filename}")
            
        except Exception as e:
            logger.error(f"خطأ في بدء التسجيل للكاميرا {self.name}: {str(e)}")
    
    def stop_recording(self):
        """إيقاف التسجيل"""
        if not self.is_recording:
            return
        
        self.is_recording = False
        
        if self.recording_writer:
            self.recording_writer.release()
            self.recording_writer = None
        
        # تحديث معلومات التسجيل في قاعدة البيانات
        if self.recording_start_time:
            duration = (datetime.now() - self.recording_start_time).total_seconds()
            self._update_recording_duration(duration)
        
        logger.info(f"تم إيقاف التسجيل للكاميرا {self.name}")
    
    def _save_recording_info(self, filename: str, filepath: str):
        """حفظ معلومات التسجيل في قاعدة البيانات"""
        try:
            conn = sqlite3.connect("database/security_cameras.db")
            conn.execute("""
                INSERT INTO recordings (camera_id, filename, file_path, start_time)
                VALUES (?, ?, ?, ?)
            """, (self.camera_id, filename, filepath, datetime.now().isoformat()))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"خطأ في حفظ معلومات التسجيل: {str(e)}")
    
    def _update_recording_duration(self, duration: float):
        """تحديث مدة التسجيل في قاعدة البيانات"""
        try:
            conn = sqlite3.connect("database/security_cameras.db")
            conn.execute("""
                UPDATE recordings 
                SET duration = ?, end_time = ?
                WHERE camera_id = ? AND end_time IS NULL
                ORDER BY start_time DESC LIMIT 1
            """, (int(duration), datetime.now().isoformat(), self.camera_id))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"خطأ في تحديث مدة التسجيل: {str(e)}")
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """الحصول على الإطار الحالي"""
        return self.last_frame.copy() if self.last_frame is not None else None


class StreamManager:
    """مدير البث المباشر الرئيسي"""
    
    def __init__(self):
        self.streams: Dict[int, CameraStream] = {}
        self.max_concurrent_streams = 10
        
    def add_camera_stream(self, camera_id: int, rtsp_url: str, name: str) -> bool:
        """إضافة بث كاميرا جديدة"""
        if len(self.streams) >= self.max_concurrent_streams:
            logger.warning("تم الوصول للحد الأقصى من البث المتزامن")
            return False
        
        if camera_id in self.streams:
            logger.warning(f"الكاميرا {camera_id} موجودة بالفعل")
            return False
        
        stream = CameraStream(camera_id, rtsp_url, name)
        if stream.start_stream():
            self.streams[camera_id] = stream
            return True
        
        return False
    
    def remove_camera_stream(self, camera_id: int) -> bool:
        """إزالة بث كاميرا"""
        if camera_id not in self.streams:
            return False
        
        self.streams[camera_id].stop_stream()
        del self.streams[camera_id]
        return True
    
    def get_camera_frame(self, camera_id: int) -> Optional[np.ndarray]:
        """الحصول على إطار من كاميرا محددة"""
        if camera_id not in self.streams:
            return None
        
        return self.streams[camera_id].get_current_frame()
    
    def start_recording(self, camera_id: int) -> bool:
        """بدء التسجيل لكاميرا محددة"""
        if camera_id not in self.streams:
            return False
        
        self.streams[camera_id].start_recording()
        return True
    
    def stop_recording(self, camera_id: int) -> bool:
        """إيقاف التسجيل لكاميرا محددة"""
        if camera_id not in self.streams:
            return False
        
        self.streams[camera_id].stop_recording()
        return True
    
    def get_active_streams(self) -> List[int]:
        """الحصول على قائمة الكاميرات النشطة"""
        return [camera_id for camera_id, stream in self.streams.items() if stream.is_active]
    
    def cleanup_old_recordings(self, days_to_keep: int = 30):
        """تنظيف التسجيلات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            conn = sqlite3.connect("database/security_cameras.db")
            old_recordings = conn.execute("""
                SELECT file_path FROM recordings 
                WHERE start_time < ?
            """, (cutoff_date.isoformat(),)).fetchall()
            
            # حذف الملفات
            for (file_path,) in old_recordings:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.error(f"خطأ في حذف الملف {file_path}: {str(e)}")
            
            # حذف السجلات من قاعدة البيانات
            conn.execute("DELETE FROM recordings WHERE start_time < ?", (cutoff_date.isoformat(),))
            conn.commit()
            conn.close()
            
            logger.info(f"تم تنظيف {len(old_recordings)} تسجيل قديم")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التسجيلات القديمة: {str(e)}")
    
    def shutdown(self):
        """إغلاق جميع البث"""
        for camera_id in list(self.streams.keys()):
            self.remove_camera_stream(camera_id)
        
        logger.info("تم إغلاق جميع البث")


# مثيل مدير البث العام
stream_manager = StreamManager()
