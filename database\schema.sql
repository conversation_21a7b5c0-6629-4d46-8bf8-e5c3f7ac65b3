-- نظام إدارة كاميرات المراقبة الأمنية
-- Database Schema

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100),
    role VARCHAR(20) DEFAULT 'viewer', -- admin, operator, viewer
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- جدول الكاميرات
CREATE TABLE IF NOT EXISTS cameras (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45) NOT NULL,
    port INTEGER DEFAULT 554,
    username VARCHAR(50),
    password VARCHAR(100),
    rtsp_url VARCHAR(500) NOT NULL,
    location VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE,
    is_recording BOOLEAN DEFAULT FALSE,
    resolution VARCHAR(20) DEFAULT '1920x1080',
    fps INTEGER DEFAULT 25,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP
);

-- جدول التسجيلات
CREATE TABLE IF NOT EXISTS recordings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    camera_id INTEGER NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER,
    duration INTEGER, -- بالثواني
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    thumbnail_path VARCHAR(500),
    is_motion_detected BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (camera_id) REFERENCES cameras(id) ON DELETE CASCADE
);

-- جدول سجلات المشاهدة
CREATE TABLE IF NOT EXISTS viewing_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    camera_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL, -- view_live, view_recording, download
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER, -- بالثواني
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (camera_id) REFERENCES cameras(id) ON DELETE CASCADE
);

-- جدول سجلات النشاطات
CREATE TABLE IF NOT EXISTS activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50), -- camera, user, recording, system
    resource_id INTEGER,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- جدول التنبيهات
CREATE TABLE IF NOT EXISTS alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    camera_id INTEGER,
    alert_type VARCHAR(50) NOT NULL, -- motion, offline, error
    title VARCHAR(200) NOT NULL,
    message TEXT,
    severity VARCHAR(20) DEFAULT 'info', -- critical, warning, info
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (camera_id) REFERENCES cameras(id) ON DELETE CASCADE
);

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_cameras_ip ON cameras(ip_address);
CREATE INDEX IF NOT EXISTS idx_recordings_camera_time ON recordings(camera_id, start_time);
CREATE INDEX IF NOT EXISTS idx_viewing_logs_user_time ON viewing_logs(user_id, start_time);
CREATE INDEX IF NOT EXISTS idx_activity_logs_time ON activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_alerts_camera_time ON alerts(camera_id, created_at);

-- إدراج بيانات أولية
INSERT OR IGNORE INTO users (username, email, password_hash, full_name, role) 
VALUES ('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'مدير النظام', 'admin');

INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description) VALUES
('max_recording_days', '30', 'عدد أيام الاحتفاظ بالتسجيلات'),
('max_concurrent_streams', '10', 'عدد البث المتزامن الأقصى'),
('motion_detection_enabled', 'true', 'تفعيل كشف الحركة'),
('auto_cleanup_enabled', 'true', 'تفعيل التنظيف التلقائي للملفات القديمة'),
('notification_email', '', 'البريد الإلكتروني للتنبيهات');
