#!/usr/bin/env python3
"""
بدء سريع لنظام إدارة كاميرات المراقبة
Quick Start for Security Camera Management System
"""

import os
import sys
import subprocess
import time

def print_banner():
    """طباعة شعار النظام"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🎥 نظام إدارة كاميرات المراقبة الأمنية 🎥           ║
    ║           Security Camera Management System                  ║
    ║                                                              ║
    ║                    الإصدار 1.0.0                           ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ تم تثبيت المتطلبات")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

def create_sample_camera():
    """إنشاء كاميرا تجريبية"""
    print("📹 إنشاء كاميرا تجريبية...")
    
    import requests
    import time
    
    # انتظار بدء السيرفر
    for i in range(10):
        try:
            response = requests.get("http://localhost:8000/", timeout=2)
            if response.status_code == 200:
                break
        except:
            time.sleep(1)
    else:
        print("⚠️  لا يمكن الاتصال بالسيرفر")
        return False
    
    # إضافة كاميرا تجريبية
    sample_camera = {
        "name": "كاميرا تجريبية",
        "description": "كاميرا ويب للاختبار",
        "ip_address": "localhost",
        "rtsp_url": "0",  # كاميرا ويب افتراضية
        "location": "مكتب الاختبار"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/cameras",
            json=sample_camera,
            timeout=5
        )
        
        if response.status_code == 200:
            print("✅ تم إنشاء كاميرا تجريبية")
            return True
        else:
            print("⚠️  لا يمكن إنشاء كاميرا تجريبية")
            return False
            
    except Exception as e:
        print(f"⚠️  خطأ في إنشاء الكاميرا: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    print("🚀 بدء التشغيل السريع...")
    print("=" * 60)
    
    # فحص إصدار Python
    if not check_python_version():
        return 1
    
    # تثبيت المتطلبات
    if not install_requirements():
        return 1
    
    print("\n🧪 تشغيل اختبار سريع...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ الاختبار السريع نجح")
        else:
            print("⚠️  الاختبار السريع فشل - سيتم المتابعة")
            print(result.stdout)
    except:
        print("⚠️  لا يمكن تشغيل الاختبار - سيتم المتابعة")
    
    print("\n🎯 تشغيل النظام...")
    print("=" * 60)
    
    try:
        # تشغيل النظام
        process = subprocess.Popen([sys.executable, "run_system.py"])
        
        # انتظار قليل ثم إنشاء كاميرا تجريبية
        time.sleep(5)
        create_sample_camera()
        
        print("\n🎉 النظام يعمل بنجاح!")
        print("📱 تطبيق العميل: نافذة منفصلة")
        print("🌐 واجهة الويب: http://localhost:8000")
        print("📚 الوثائق: http://localhost:8000/docs")
        print("\n💡 نصائح:")
        print("- استخدم كاميرا ويب USB للاختبار")
        print("- أضف كاميرات IP حقيقية من التطبيق")
        print("- اضغط Ctrl+C لإيقاف النظام")
        
        # انتظار إنهاء العملية
        process.wait()
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
