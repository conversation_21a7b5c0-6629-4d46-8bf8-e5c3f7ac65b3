"""
نظام إدارة كاميرات المراقبة الأمنية - السيرفر المركزي
Security Camera Management System - Backend API Server
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from datetime import datetime, timedelta
from typing import List, Optional
import sqlite3
import json
import asyncio
from pathlib import Path

# إنشاء التطبيق
app = FastAPI(
    title="نظام إدارة كاميرات المراقبة",
    description="API لإدارة كاميرات المراقبة الأمنية",
    version="1.0.0"
)

# إعداد CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# إعداد المصادقة
security = HTTPBearer()

# إعداد قاعدة البيانات
DATABASE_PATH = "database/security_cameras.db"
RECORDINGS_PATH = "storage/recordings"
THUMBNAILS_PATH = "storage/thumbnails"

# إنشاء المجلدات المطلوبة
os.makedirs(os.path.dirname(DATABASE_PATH), exist_ok=True)
os.makedirs(RECORDINGS_PATH, exist_ok=True)
os.makedirs(THUMBNAILS_PATH, exist_ok=True)

def init_database():
    """تهيئة قاعدة البيانات"""
    with open("database/schema.sql", "r", encoding="utf-8") as f:
        schema = f.read()
    
    conn = sqlite3.connect(DATABASE_PATH)
    conn.executescript(schema)
    conn.close()

def get_db_connection():
    """الحصول على اتصال قاعدة البيانات"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

# نماذج البيانات
from pydantic import BaseModel

class CameraCreate(BaseModel):
    name: str
    description: Optional[str] = None
    ip_address: str
    port: int = 554
    username: Optional[str] = None
    password: Optional[str] = None
    rtsp_url: str
    location: Optional[str] = None
    resolution: str = "1920x1080"
    fps: int = 25

class CameraUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    ip_address: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    rtsp_url: Optional[str] = None
    location: Optional[str] = None
    is_active: Optional[bool] = None
    is_recording: Optional[bool] = None
    resolution: Optional[str] = None
    fps: Optional[int] = None

class UserLogin(BaseModel):
    username: str
    password: str

# مسارات API

@app.on_event("startup")
async def startup_event():
    """تهيئة النظام عند البدء"""
    init_database()
    print("✅ تم تهيئة قاعدة البيانات")

@app.get("/")
async def root():
    """الصفحة الرئيسية"""
    return {
        "message": "نظام إدارة كاميرات المراقبة الأمنية",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/cameras")
async def get_cameras():
    """الحصول على قائمة الكاميرات"""
    conn = get_db_connection()
    cameras = conn.execute("""
        SELECT id, name, description, ip_address, port, location, 
               is_active, is_recording, resolution, fps, 
               created_at, last_seen
        FROM cameras 
        ORDER BY name
    """).fetchall()
    conn.close()
    
    return [dict(camera) for camera in cameras]

@app.post("/api/cameras")
async def create_camera(camera: CameraCreate):
    """إضافة كاميرا جديدة"""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO cameras (name, description, ip_address, port, username, 
                               password, rtsp_url, location, resolution, fps)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            camera.name, camera.description, camera.ip_address, camera.port,
            camera.username, camera.password, camera.rtsp_url, camera.location,
            camera.resolution, camera.fps
        ))
        
        camera_id = cursor.lastrowid
        conn.commit()
        
        # تسجيل النشاط
        conn.execute("""
            INSERT INTO activity_logs (action, resource_type, resource_id, details)
            VALUES (?, ?, ?, ?)
        """, ("create_camera", "camera", camera_id, f"تم إضافة كاميرا جديدة: {camera.name}"))
        conn.commit()
        
        return {"message": "تم إضافة الكاميرا بنجاح", "camera_id": camera_id}
    
    except sqlite3.IntegrityError:
        raise HTTPException(status_code=400, detail="عنوان IP مستخدم بالفعل")
    finally:
        conn.close()

@app.put("/api/cameras/{camera_id}")
async def update_camera(camera_id: int, camera: CameraUpdate):
    """تحديث بيانات كاميرا"""
    conn = get_db_connection()
    
    # التحقق من وجود الكاميرا
    existing = conn.execute("SELECT id FROM cameras WHERE id = ?", (camera_id,)).fetchone()
    if not existing:
        conn.close()
        raise HTTPException(status_code=404, detail="الكاميرا غير موجودة")
    
    # بناء استعلام التحديث
    updates = []
    values = []
    
    for field, value in camera.dict(exclude_unset=True).items():
        updates.append(f"{field} = ?")
        values.append(value)
    
    if updates:
        values.append(datetime.now().isoformat())
        values.append(camera_id)
        
        conn.execute(f"""
            UPDATE cameras 
            SET {', '.join(updates)}, updated_at = ?
            WHERE id = ?
        """, values)
        
        conn.execute("""
            INSERT INTO activity_logs (action, resource_type, resource_id, details)
            VALUES (?, ?, ?, ?)
        """, ("update_camera", "camera", camera_id, "تم تحديث بيانات الكاميرا"))
        
        conn.commit()
    
    conn.close()
    return {"message": "تم تحديث الكاميرا بنجاح"}

@app.delete("/api/cameras/{camera_id}")
async def delete_camera(camera_id: int):
    """حذف كاميرا"""
    conn = get_db_connection()
    
    # التحقق من وجود الكاميرا
    camera = conn.execute("SELECT name FROM cameras WHERE id = ?", (camera_id,)).fetchone()
    if not camera:
        conn.close()
        raise HTTPException(status_code=404, detail="الكاميرا غير موجودة")
    
    # حذف الكاميرا
    conn.execute("DELETE FROM cameras WHERE id = ?", (camera_id,))
    
    # تسجيل النشاط
    conn.execute("""
        INSERT INTO activity_logs (action, resource_type, resource_id, details)
        VALUES (?, ?, ?, ?)
    """, ("delete_camera", "camera", camera_id, f"تم حذف الكاميرا: {camera['name']}"))
    
    conn.commit()
    conn.close()
    
    return {"message": "تم حذف الكاميرا بنجاح"}

@app.get("/api/cameras/{camera_id}/stream")
async def get_camera_stream(camera_id: int):
    """الحصول على رابط البث المباشر للكاميرا"""
    conn = get_db_connection()
    camera = conn.execute("""
        SELECT rtsp_url, is_active 
        FROM cameras 
        WHERE id = ?
    """, (camera_id,)).fetchone()
    conn.close()
    
    if not camera:
        raise HTTPException(status_code=404, detail="الكاميرا غير موجودة")
    
    if not camera['is_active']:
        raise HTTPException(status_code=400, detail="الكاميرا غير نشطة")
    
    return {
        "camera_id": camera_id,
        "stream_url": camera['rtsp_url'],
        "status": "active"
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
