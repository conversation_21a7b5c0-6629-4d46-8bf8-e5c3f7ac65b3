{"name": "security-camera-backend", "version": "1.0.0", "description": "Backend server for security camera management system", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "migrate": "knex migrate:latest", "seed": "knex seed:run"}, "keywords": ["security", "camera", "surveillance", "monitoring"], "author": "Security Camera System", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "knex": "^2.5.1", "redis": "^4.6.8", "socket.io": "^4.7.2", "multer": "^1.4.5-lts.1", "fluent-ffmpeg": "^2.1.2", "node-rtsp-stream": "^0.0.9", "ws": "^8.13.0", "joi": "^17.9.2", "winston": "^3.10.0", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "@types/jest": "^29.5.4"}, "engines": {"node": ">=18.0.0"}}