#!/usr/bin/env python3
"""
اختبار نظام إدارة كاميرات المراقبة الأمنية
Security Camera Management System - Test Suite
"""

import requests
import json
import time
import sqlite3
import os
from pathlib import Path
import cv2
import threading

class SystemTester:
    def __init__(self):
        self.api_base_url = "http://localhost:8000/api"
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """تسجيل نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")
    
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            db_path = "database/security_cameras.db"
            if not os.path.exists(db_path):
                self.log_test("اتصال قاعدة البيانات", False, "ملف قاعدة البيانات غير موجود")
                return False
            
            conn = sqlite3.connect(db_path)
            cursor = conn.execute("SELECT COUNT(*) FROM users")
            count = cursor.fetchone()[0]
            conn.close()
            
            self.log_test("اتصال قاعدة البيانات", True, f"تم العثور على {count} مستخدم")
            return True
            
        except Exception as e:
            self.log_test("اتصال قاعدة البيانات", False, str(e))
            return False
    
    def test_api_server(self):
        """اختبار السيرفر المركزي"""
        try:
            response = requests.get(f"{self.api_base_url.replace('/api', '')}/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.log_test("السيرفر المركزي", True, f"الإصدار: {data.get('version', 'غير محدد')}")
                return True
            else:
                self.log_test("السيرفر المركزي", False, f"رمز الحالة: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_test("السيرفر المركزي", False, f"خطأ في الاتصال: {str(e)}")
            return False
    
    def test_cameras_api(self):
        """اختبار API الكاميرات"""
        try:
            # اختبار الحصول على قائمة الكاميرات
            response = requests.get(f"{self.api_base_url}/cameras", timeout=5)
            if response.status_code == 200:
                cameras = response.json()
                self.log_test("API قائمة الكاميرات", True, f"تم العثور على {len(cameras)} كاميرا")
            else:
                self.log_test("API قائمة الكاميرات", False, f"رمز الحالة: {response.status_code}")
                return False
            
            # اختبار إضافة كاميرا جديدة
            test_camera = {
                "name": "كاميرا اختبار",
                "description": "كاميرا للاختبار التلقائي",
                "ip_address": "192.168.1.999",
                "rtsp_url": "rtsp://test:test@192.168.1.999:554/stream",
                "location": "مختبر الاختبار"
            }
            
            response = requests.post(f"{self.api_base_url}/cameras", json=test_camera, timeout=5)
            if response.status_code == 200:
                camera_data = response.json()
                camera_id = camera_data.get("camera_id")
                self.log_test("API إضافة كاميرا", True, f"تم إنشاء الكاميرا برقم: {camera_id}")
                
                # اختبار حذف الكاميرا
                if camera_id:
                    delete_response = requests.delete(f"{self.api_base_url}/cameras/{camera_id}", timeout=5)
                    if delete_response.status_code == 200:
                        self.log_test("API حذف كاميرا", True, "تم حذف كاميرا الاختبار")
                    else:
                        self.log_test("API حذف كاميرا", False, f"رمز الحالة: {delete_response.status_code}")
                
                return True
            else:
                self.log_test("API إضافة كاميرا", False, f"رمز الحالة: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.log_test("API الكاميرات", False, f"خطأ في الاتصال: {str(e)}")
            return False
    
    def test_opencv_functionality(self):
        """اختبار وظائف OpenCV"""
        try:
            # اختبار إنشاء كائن VideoCapture
            cap = cv2.VideoCapture(0)  # كاميرا ويب افتراضية
            
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                
                if ret and frame is not None:
                    self.log_test("OpenCV - كاميرا ويب", True, f"حجم الإطار: {frame.shape}")
                else:
                    self.log_test("OpenCV - كاميرا ويب", False, "لا يمكن قراءة إطار من الكاميرا")
            else:
                self.log_test("OpenCV - كاميرا ويب", False, "لا توجد كاميرا ويب متاحة")
            
            # اختبار معالجة الصور
            import numpy as np
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
            
            if gray.shape == (100, 100):
                self.log_test("OpenCV - معالجة الصور", True, "تحويل الألوان يعمل بشكل صحيح")
            else:
                self.log_test("OpenCV - معالجة الصور", False, "مشكلة في تحويل الألوان")
            
            return True
            
        except Exception as e:
            self.log_test("OpenCV", False, str(e))
            return False
    
    def test_directory_structure(self):
        """اختبار هيكل المجلدات"""
        required_dirs = [
            "database",
            "storage/recordings",
            "storage/thumbnails",
            "storage/logs",
            "backend",
            "client"
        ]
        
        all_exist = True
        missing_dirs = []
        
        for directory in required_dirs:
            if os.path.exists(directory):
                self.log_test(f"مجلد {directory}", True, "موجود")
            else:
                self.log_test(f"مجلد {directory}", False, "مفقود")
                missing_dirs.append(directory)
                all_exist = False
        
        if all_exist:
            self.log_test("هيكل المجلدات", True, "جميع المجلدات موجودة")
        else:
            self.log_test("هيكل المجلدات", False, f"مجلدات مفقودة: {', '.join(missing_dirs)}")
        
        return all_exist
    
    def test_required_files(self):
        """اختبار الملفات المطلوبة"""
        required_files = [
            "requirements.txt",
            "run_system.py",
            "backend/main.py",
            "client/main.py",
            "database/schema.sql",
            ".env.example"
        ]
        
        all_exist = True
        missing_files = []
        
        for file_path in required_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                self.log_test(f"ملف {file_path}", True, f"الحجم: {size} بايت")
            else:
                self.log_test(f"ملف {file_path}", False, "مفقود")
                missing_files.append(file_path)
                all_exist = False
        
        if all_exist:
            self.log_test("الملفات المطلوبة", True, "جميع الملفات موجودة")
        else:
            self.log_test("الملفات المطلوبة", False, f"ملفات مفقودة: {', '.join(missing_files)}")
        
        return all_exist
    
    def test_python_dependencies(self):
        """اختبار التبعيات Python"""
        required_packages = [
            ("fastapi", "FastAPI"),
            ("uvicorn", "Uvicorn"),
            ("cv2", "OpenCV"),
            ("requests", "Requests"),
            ("PIL", "Pillow"),
            ("sqlite3", "SQLite3"),
            ("tkinter", "Tkinter")
        ]
        
        all_available = True
        missing_packages = []
        
        for package_name, display_name in required_packages:
            try:
                if package_name == "cv2":
                    import cv2
                    version = cv2.__version__
                elif package_name == "PIL":
                    from PIL import Image
                    version = Image.__version__ if hasattr(Image, '__version__') else "متاح"
                else:
                    module = __import__(package_name)
                    version = getattr(module, '__version__', 'متاح')
                
                self.log_test(f"مكتبة {display_name}", True, f"الإصدار: {version}")
                
            except ImportError:
                self.log_test(f"مكتبة {display_name}", False, "غير مثبتة")
                missing_packages.append(package_name)
                all_available = False
        
        if all_available:
            self.log_test("التبعيات Python", True, "جميع المكتبات متاحة")
        else:
            self.log_test("التبعيات Python", False, f"مكتبات مفقودة: {', '.join(missing_packages)}")
        
        return all_available
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار نظام إدارة كاميرات المراقبة")
        print("=" * 60)
        
        # اختبارات البنية الأساسية
        print("\n📁 اختبار البنية الأساسية:")
        self.test_directory_structure()
        self.test_required_files()
        
        # اختبار التبعيات
        print("\n📦 اختبار التبعيات:")
        self.test_python_dependencies()
        
        # اختبار قاعدة البيانات
        print("\n🗄️  اختبار قاعدة البيانات:")
        self.test_database_connection()
        
        # اختبار OpenCV
        print("\n🎥 اختبار معالجة الفيديو:")
        self.test_opencv_functionality()
        
        # اختبار السيرفر (إذا كان يعمل)
        print("\n🌐 اختبار السيرفر المركزي:")
        if self.test_api_server():
            self.test_cameras_api()
        else:
            print("⚠️  السيرفر غير متاح - تخطي اختبارات API")
        
        # تلخيص النتائج
        self.print_summary()
    
    def print_summary(self):
        """طباعة ملخص النتائج"""
        print("\n" + "=" * 60)
        print("📊 ملخص نتائج الاختبار")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"إجمالي الاختبارات: {total_tests}")
        print(f"✅ نجح: {passed_tests}")
        print(f"❌ فشل: {failed_tests}")
        print(f"📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ الاختبارات الفاشلة:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n" + "=" * 60)
        
        if failed_tests == 0:
            print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        else:
            print("⚠️  يرجى إصلاح المشاكل المذكورة أعلاه قبل تشغيل النظام.")
        
        return failed_tests == 0


def main():
    """الدالة الرئيسية"""
    tester = SystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 يمكنك الآن تشغيل النظام باستخدام:")
        print("python run_system.py")
    else:
        print("\n🔧 يرجى مراجعة دليل التثبيت:")
        print("cat INSTALL.md")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
