version: '3.8'

services:
  # السيرفر المركزي
  security-camera-backend:
    build: .
    container_name: security-camera-backend
    ports:
      - "8000:8000"
    volumes:
      - ./database:/app/database
      - ./storage:/app/storage
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=sqlite:///database/security_cameras.db
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8000
      - DEBUG_MODE=false
    restart: unless-stopped
    networks:
      - security-camera-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # قاعدة بيانات PostgreSQL (اختياري)
  postgres:
    image: postgres:15-alpine
    container_name: security-camera-postgres
    environment:
      POSTGRES_DB: security_cameras
      POSTGRES_USER: security_user
      POSTGRES_PASSWORD: secure_password_123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - security-camera-network
    profiles:
      - postgres

  # Redis للتخزين المؤقت (اختياري)
  redis:
    image: redis:7-alpine
    container_name: security-camera-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - security-camera-network
    profiles:
      - redis

  # Nginx كوكيل عكسي (للإنتاج)
  nginx:
    image: nginx:alpine
    container_name: security-camera-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - security-camera-backend
    restart: unless-stopped
    networks:
      - security-camera-network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  security-camera-network:
    driver: bridge
