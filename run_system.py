#!/usr/bin/env python3
"""
نظام إدارة كاميرات المراقبة الأمنية
Security Camera Management System - System Launcher

هذا الملف يقوم بتشغيل النظام كاملاً:
- السيرفر المركزي (Backend API)
- تطبيق العميل (Client Application)
"""

import subprocess
import sys
import os
import time
import threading
import signal
from pathlib import Path

class SystemLauncher:
    def __init__(self):
        self.backend_process = None
        self.client_process = None
        self.running = True
        
    def check_dependencies(self):
        """فحص التبعيات المطلوبة"""
        print("🔍 فحص التبعيات...")
        
        required_packages = [
            'fastapi', 'uvicorn', 'opencv-python', 'requests', 
            'pillow', 'tkinter'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                if package == 'tkinter':
                    import tkinter
                else:
                    __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} - مفقود")
        
        if missing_packages:
            print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
            print("يرجى تثبيتها باستخدام:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ جميع التبعيات متوفرة")
        return True
    
    def setup_directories(self):
        """إنشاء المجلدات المطلوبة"""
        print("📁 إنشاء المجلدات...")
        
        directories = [
            "database",
            "storage/recordings",
            "storage/thumbnails",
            "storage/logs"
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            print(f"✅ {directory}")
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        print("🗄️  تهيئة قاعدة البيانات...")
        
        try:
            import sqlite3
            
            # قراءة مخطط قاعدة البيانات
            with open("database/schema.sql", "r", encoding="utf-8") as f:
                schema = f.read()
            
            # إنشاء قاعدة البيانات
            conn = sqlite3.connect("database/security_cameras.db")
            conn.executescript(schema)
            conn.close()
            
            print("✅ تم تهيئة قاعدة البيانات")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
            return False
    
    def start_backend(self):
        """تشغيل السيرفر المركزي"""
        print("🚀 تشغيل السيرفر المركزي...")
        
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "backend.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--reload"
            ], cwd=os.getcwd())
            
            # انتظار بدء السيرفر
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✅ السيرفر المركزي يعمل على http://localhost:8000")
                return True
            else:
                print("❌ فشل في تشغيل السيرفر المركزي")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل السيرفر: {str(e)}")
            return False
    
    def start_client(self):
        """تشغيل تطبيق العميل"""
        print("🖥️  تشغيل تطبيق العميل...")
        
        try:
            self.client_process = subprocess.Popen([
                sys.executable, "client/main.py"
            ], cwd=os.getcwd())
            
            time.sleep(2)
            
            if self.client_process.poll() is None:
                print("✅ تطبيق العميل يعمل")
                return True
            else:
                print("❌ فشل في تشغيل تطبيق العميل")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل تطبيق العميل: {str(e)}")
            return False
    
    def monitor_processes(self):
        """مراقبة العمليات"""
        while self.running:
            try:
                # فحص السيرفر المركزي
                if self.backend_process and self.backend_process.poll() is not None:
                    print("⚠️  السيرفر المركزي توقف")
                    self.running = False
                    break
                
                # فحص تطبيق العميل
                if self.client_process and self.client_process.poll() is not None:
                    print("⚠️  تطبيق العميل توقف")
                    self.running = False
                    break
                
                time.sleep(5)
                
            except KeyboardInterrupt:
                break
    
    def shutdown(self):
        """إغلاق النظام"""
        print("\n🛑 إغلاق النظام...")
        self.running = False
        
        if self.client_process:
            try:
                self.client_process.terminate()
                self.client_process.wait(timeout=5)
                print("✅ تم إغلاق تطبيق العميل")
            except:
                self.client_process.kill()
                print("⚠️  تم إنهاء تطبيق العميل بالقوة")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ تم إغلاق السيرفر المركزي")
            except:
                self.backend_process.kill()
                print("⚠️  تم إنهاء السيرفر المركزي بالقوة")
        
        print("👋 تم إغلاق النظام")
    
    def run(self):
        """تشغيل النظام الكامل"""
        print("=" * 60)
        print("🎥 نظام إدارة كاميرات المراقبة الأمنية")
        print("Security Camera Management System")
        print("=" * 60)
        
        # فحص التبعيات
        if not self.check_dependencies():
            return False
        
        # إنشاء المجلدات
        self.setup_directories()
        
        # تهيئة قاعدة البيانات
        if not self.init_database():
            return False
        
        # تشغيل السيرفر المركزي
        if not self.start_backend():
            return False
        
        # تشغيل تطبيق العميل
        if not self.start_client():
            self.shutdown()
            return False
        
        print("\n🎉 النظام يعمل بنجاح!")
        print("📱 تطبيق العميل: نافذة منفصلة")
        print("🌐 واجهة الويب: http://localhost:8000")
        print("📚 الوثائق: http://localhost:8000/docs")
        print("\nاضغط Ctrl+C لإيقاف النظام")
        
        # إعداد معالج الإشارات
        def signal_handler(signum, frame):
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # مراقبة العمليات
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        try:
            # انتظار إنهاء العمليات
            if self.backend_process:
                self.backend_process.wait()
            if self.client_process:
                self.client_process.wait()
        except KeyboardInterrupt:
            pass
        finally:
            self.shutdown()
        
        return True


def main():
    """الدالة الرئيسية"""
    launcher = SystemLauncher()
    
    try:
        success = launcher.run()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  تم إيقاف النظام بواسطة المستخدم")
        launcher.shutdown()
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        launcher.shutdown()
        sys.exit(1)


if __name__ == "__main__":
    main()
