# إعدادات نظام إدارة كاميرات المراقبة الأمنية
# Security Camera Management System Configuration

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///database/security_cameras.db
# للاستخدام مع PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost:5432/security_cameras

# إعدادات السيرفر
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
DEBUG_MODE=true

# إعدادات الأمان
SECRET_KEY=your-secret-key-here-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440

# إعدادات التسجيل
MAX_RECORDING_DAYS=30
AUTO_CLEANUP_ENABLED=true
RECORDINGS_PATH=storage/recordings
THUMBNAILS_PATH=storage/thumbnails

# إعدادات البث المباشر
MAX_CONCURRENT_STREAMS=10
DEFAULT_STREAM_QUALITY=720p
MOTION_DETECTION_ENABLED=true
MOTION_SENSITIVITY=1000

# إعدادات التنبيهات
NOTIFICATIONS_ENABLED=true
EMAIL_NOTIFICATIONS=false
SMTP_SERVER=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
NOTIFICATION_EMAIL=

# إعدادات Redis (اختياري للتخزين المؤقت)
REDIS_URL=redis://localhost:6379/0
REDIS_ENABLED=false

# إعدادات السجلات
LOG_LEVEL=INFO
LOG_FILE=storage/logs/system.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# إعدادات الأداء
WORKER_PROCESSES=1
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65

# إعدادات الأمان المتقدمة
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_MINUTE=100

# إعدادات الملفات
MAX_UPLOAD_SIZE=100MB
ALLOWED_VIDEO_FORMATS=["mp4", "avi", "mkv", "mov"]
ALLOWED_IMAGE_FORMATS=["jpg", "jpeg", "png", "bmp"]

# إعدادات النسخ الاحتياطي
BACKUP_ENABLED=false
BACKUP_INTERVAL_HOURS=24
BACKUP_RETENTION_DAYS=7
BACKUP_PATH=storage/backups
