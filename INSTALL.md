# دليل التثبيت والتشغيل
# Installation and Setup Guide

## متطلبات النظام

### متطلبات البرمجيات
- **Python 3.8+** (يُفضل Python 3.10 أو أحدث)
- **pip** (مدي<PERSON> حزم Python)
- **Git** (لاستنساخ المشروع)
- **FFmpeg** (لمعالجة الفيديو)

### متطلبات الأجهزة
- **المعالج**: Intel i5 أو AMD Ryzen 5 (أو أفضل)
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مُوصى به)
- **التخزين**: 100 GB مساحة فارغة (للتسجيلات)
- **الشبكة**: اتصال إنترنت مستقر

## خطوات التثبيت

### 1. تحضير البيئة

```bash
# تحديث النظام (Ubuntu/Debian)
sudo apt update && sudo apt upgrade -y

# تثبيت Python و pip
sudo apt install python3 python3-pip python3-venv -y

# تثبيت FFmpeg
sudo apt install ffmpeg -y

# تثبيت مكتبات النظام المطلوبة
sudo apt install python3-tk python3-dev build-essential -y
```

### 2. استنساخ المشروع

```bash
# استنساخ المشروع
git clone <repository-url>
cd security-camera-system

# أو تحميل الملفات يدوياً إذا لم يكن Git متاحاً
```

### 3. إنشاء البيئة الافتراضية

```bash
# إنشاء البيئة الافتراضية
python3 -m venv venv

# تفعيل البيئة الافتراضية
# على Linux/Mac:
source venv/bin/activate

# على Windows:
venv\Scripts\activate
```

### 4. تثبيت التبعيات

```bash
# تثبيت جميع المكتبات المطلوبة
pip install -r requirements.txt

# في حالة وجود مشاكل، ثبت المكتبات الأساسية يدوياً:
pip install fastapi uvicorn opencv-python requests pillow
```

### 5. إعداد الإعدادات

```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تعديل الإعدادات حسب الحاجة
nano .env
```

### 6. تشغيل النظام

```bash
# تشغيل النظام الكامل
python run_system.py

# أو تشغيل المكونات منفصلة:

# تشغيل السيرفر فقط
cd backend
python main.py

# تشغيل العميل فقط (في terminal آخر)
cd client
python main.py
```

## التحقق من التثبيت

### 1. فحص السيرفر المركزي
- افتح المتصفح واذهب إلى: `http://localhost:8000`
- يجب أن ترى رسالة ترحيب من النظام
- للوثائق التفاعلية: `http://localhost:8000/docs`

### 2. فحص تطبيق العميل
- يجب أن تظهر نافذة التطبيق تلقائياً
- تحقق من إمكانية الاتصال بالسيرفر

### 3. اختبار إضافة كاميرا
```bash
# اختبار API باستخدام curl
curl -X POST "http://localhost:8000/api/cameras" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "كاميرا اختبار",
       "ip_address": "*************",
       "rtsp_url": "rtsp://*************:554/stream"
     }'
```

## إعداد كاميرات حقيقية

### كاميرات IP
```json
{
  "name": "كاميرا المدخل الرئيسي",
  "description": "كاميرا مراقبة المدخل",
  "ip_address": "*************",
  "port": 554,
  "username": "admin",
  "password": "password123",
  "rtsp_url": "rtsp://admin:password123@*************:554/stream1",
  "location": "المدخل الرئيسي",
  "resolution": "1920x1080",
  "fps": 25
}
```

### كاميرات USB (للاختبار)
```json
{
  "name": "كاميرا USB",
  "description": "كاميرا ويب للاختبار",
  "ip_address": "localhost",
  "rtsp_url": "0",
  "location": "مكتب الاختبار"
}
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في تثبيت OpenCV
```bash
# حل بديل لـ OpenCV
pip uninstall opencv-python
pip install opencv-python-headless
```

#### 2. مشكلة في Tkinter
```bash
# على Ubuntu/Debian
sudo apt install python3-tk

# على CentOS/RHEL
sudo yum install tkinter
```

#### 3. خطأ في الاتصال بالكاميرا
- تحقق من عنوان IP للكاميرا
- تأكد من صحة بيانات تسجيل الدخول
- اختبر الرابط في VLC Media Player أولاً

#### 4. مشكلة في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm database/security_cameras.db
python run_system.py
```

#### 5. مشكلة في المنافذ
```bash
# تحقق من المنافذ المستخدمة
netstat -tulpn | grep :8000

# تغيير المنفذ في ملف .env
SERVER_PORT=8001
```

## الأمان والحماية

### 1. تغيير كلمة المرور الافتراضية
- المستخدم الافتراضي: `admin`
- كلمة المرور الافتراضية: `admin123`
- **يجب تغييرها فوراً في الإنتاج!**

### 2. تأمين الشبكة
- استخدم HTTPS في الإنتاج
- قم بتكوين جدار الحماية
- استخدم VPN للوصول عن بُعد

### 3. النسخ الاحتياطية
```bash
# نسخ احتياطي لقاعدة البيانات
cp database/security_cameras.db backup/

# نسخ احتياطي للتسجيلات
tar -czf recordings_backup.tar.gz storage/recordings/
```

## الدعم والمساعدة

### سجلات النظام
- سجلات السيرفر: `storage/logs/system.log`
- سجلات التطبيق: في terminal

### معلومات مفيدة
- **الوثائق**: `http://localhost:8000/docs`
- **حالة النظام**: `http://localhost:8000/`
- **قاعدة البيانات**: `database/security_cameras.db`

### الحصول على المساعدة
1. تحقق من سجلات الأخطاء
2. راجع هذا الدليل
3. ابحث في المشاكل الشائعة
4. اتصل بفريق الدعم التقني

---

**ملاحظة**: هذا النظام مصمم للاستخدام في البيئات المحلية والشبكات الخاصة. للاستخدام في الإنتاج، يُنصح بإجراء تقييم أمني شامل وتطبيق أفضل الممارسات الأمنية.
