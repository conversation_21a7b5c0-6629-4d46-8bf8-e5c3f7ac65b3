# نظام إدارة كاميرات المراقبة الأمنية

## نظرة عامة
نظام شامل لإدارة واستعراض كاميرات المراقبة الأمنية مع قاعدة بيانات وسيرفر مركزي.

## الميزات الرئيسية
- 📹 إدارة متقدمة للكاميرات
- 🔴 بث مباشر عالي الجودة
- 💾 تسجيل وأرشفة تلقائية
- 👥 إدارة المستخدمين والصلاحيات
- 🔔 نظام تنبيهات ذكي
- 🎛️ التحكم عن بُعد (PTZ)
- 🔍 بحث متقدم في التسجيلات
- 📱 واجهات متعددة (ويب، موبايل، سطح مكتب)

## البنية التقنية

### Backend
- **Node.js** مع Express.js
- **PostgreSQL** لقاعدة البيانات الرئيسية
- **Redis** للتخزين المؤقت
- **Socket.io** للاتصال المباشر
- **FFmpeg** لمعالجة الفيديو

### Frontend
- **React.js** لواجهة الويب
- **WebRTC** للبث المباشر
- **Material-UI** للتصميم

### الأمان
- **JWT** للمصادقة
- **HTTPS/WSS** للاتصالات المشفرة
- **RBAC** لإدارة الصلاحيات

## هيكل المشروع
```
security-camera-system/
├── backend/                 # السيرفر المركزي
│   ├── src/
│   │   ├── controllers/     # تحكم في العمليات
│   │   ├── models/          # نماذج قاعدة البيانات
│   │   ├── routes/          # مسارات API
│   │   ├── services/        # خدمات النظام
│   │   ├── middleware/      # وسطاء المعالجة
│   │   └── utils/           # أدوات مساعدة
│   ├── config/              # إعدادات النظام
│   └── tests/               # اختبارات الوحدة
├── frontend/                # واجهة المستخدم
│   ├── src/
│   │   ├── components/      # مكونات React
│   │   ├── pages/           # صفحات التطبيق
│   │   ├── services/        # خدمات API
│   │   ├── hooks/           # React Hooks
│   │   └── utils/           # أدوات مساعدة
│   └── public/              # ملفات عامة
├── database/                # قاعدة البيانات
│   ├── migrations/          # تحديثات قاعدة البيانات
│   ├── seeds/               # بيانات أولية
│   └── schemas/             # مخططات الجداول
├── storage/                 # تخزين الملفات
│   ├── recordings/          # التسجيلات
│   ├── thumbnails/          # صور مصغرة
│   └── logs/                # ملفات السجل
└── docs/                    # الوثائق
```

## متطلبات النظام
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- FFmpeg 4.4+
- مساحة تخزين كافية للتسجيلات

## التثبيت والتشغيل
```bash
# استنساخ المشروع
git clone <repository-url>
cd security-camera-system

# تثبيت التبعيات للخادم
cd backend
npm install

# تثبيت التبعيات للواجهة
cd ../frontend
npm install

# إعداد قاعدة البيانات
cd ../database
npm run migrate

# تشغيل النظام
npm run dev
```

## الإعدادات
انسخ ملف `.env.example` إلى `.env` وقم بتعديل الإعدادات حسب بيئتك.

## المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.
