# نظام إدارة كاميرات المراقبة الأمنية

## نظرة عامة
نظام شامل لإدارة واستعراض كاميرات المراقبة الأمنية مع قاعدة بيانات وسيرفر مركزي.

## الميزات الرئيسية
- 📹 إدارة متقدمة للكاميرات
- 🔴 بث مباشر عالي الجودة
- 💾 تسجيل وأرشفة تلقائية
- 👥 إدارة المستخدمين والصلاحيات
- 🔔 نظام تنبيهات ذكي
- 🎛️ التحكم عن بُعد (PTZ)
- 🔍 بحث متقدم في التسجيلات
- 📱 واجهات متعددة (ويب، موبايل، سطح مكتب)

## البنية التقنية

### Backend
- **Python** مع FastAPI
- **SQLite/PostgreSQL** لقاعدة البيانات
- **Redis** للتخزين المؤقت (اختياري)
- **WebSocket** للاتصال المباشر
- **OpenCV** لمعالجة الفيديو
- **FFmpeg** للتسجيل والبث

### Frontend/Client
- **Python** مع Tkinter أو PyQt
- **OpenCV** لعرض البث المباشر
- **Requests** للتواصل مع API
- **Threading** للعمليات المتزامنة

### الأمان
- **JWT** للمصادقة
- **HTTPS** للاتصالات المشفرة
- **SQLAlchemy** لحماية قاعدة البيانات

## هيكل المشروع
```
security-camera-system/
├── backend/                 # السيرفر المركزي
│   ├── src/
│   │   ├── controllers/     # تحكم في العمليات
│   │   ├── models/          # نماذج قاعدة البيانات
│   │   ├── routes/          # مسارات API
│   │   ├── services/        # خدمات النظام
│   │   ├── middleware/      # وسطاء المعالجة
│   │   └── utils/           # أدوات مساعدة
│   ├── config/              # إعدادات النظام
│   └── tests/               # اختبارات الوحدة
├── frontend/                # واجهة المستخدم
│   ├── src/
│   │   ├── components/      # مكونات React
│   │   ├── pages/           # صفحات التطبيق
│   │   ├── services/        # خدمات API
│   │   ├── hooks/           # React Hooks
│   │   └── utils/           # أدوات مساعدة
│   └── public/              # ملفات عامة
├── database/                # قاعدة البيانات
│   ├── migrations/          # تحديثات قاعدة البيانات
│   ├── seeds/               # بيانات أولية
│   └── schemas/             # مخططات الجداول
├── storage/                 # تخزين الملفات
│   ├── recordings/          # التسجيلات
│   ├── thumbnails/          # صور مصغرة
│   └── logs/                # ملفات السجل
└── docs/                    # الوثائق
```

## متطلبات النظام
- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- FFmpeg 4.4+
- مساحة تخزين كافية للتسجيلات

## التثبيت والتشغيل

### التشغيل السريع
```bash
# تحميل المشروع
git clone <repository-url>
cd security-camera-system

# تشغيل سريع (يثبت المتطلبات تلقائياً)
python quick_start.py
```

### التثبيت اليدوي
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# اختبار النظام
python test_system.py

# تشغيل النظام الكامل
python run_system.py
```

### التشغيل باستخدام Docker
```bash
# بناء وتشغيل الحاوية
docker-compose up --build

# للإنتاج مع PostgreSQL
docker-compose --profile postgres up -d
```

## الإعدادات
انسخ ملف `.env.example` إلى `.env` وقم بتعديل الإعدادات حسب بيئتك.

## المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT.
